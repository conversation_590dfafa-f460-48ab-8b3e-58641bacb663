package com.wexl.retail.student.mlp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
public class StudentsMlpOverview {

  @JsonProperty("student_details")
  List<StudentDetails> studentDetails;
}
