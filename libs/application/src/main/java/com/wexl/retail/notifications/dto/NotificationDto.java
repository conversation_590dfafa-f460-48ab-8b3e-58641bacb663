package com.wexl.retail.notifications.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.communications.circulars.service.CommunicationFeature;
import com.wexl.retail.messagetemplate.category.model.MessageTemplateCategory;
import com.wexl.retail.model.Teacher;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import lombok.Builder;

public record NotificationDto() {

  @Builder
  public record NotificationRequest(
      @NotNull String title,
      @JsonProperty("notification_type") @NotNull NotificationType notificationType,
      @JsonProperty("student_ids") List<Long> studentIds,
      @JsonProperty("staff_auth_id") String staffAuthId,
      @JsonProperty("user_auth_id") String userAuthId,
      @JsonProperty("classroom_ids") List<Long> classroomIds,
      @JsonProperty("section_uuids") List<String> sectionUuids,
      @JsonProperty("org_slugs") List<String> orgSlugs,
      String message,
      List<String> attachment,
      List<String> link,
      @JsonProperty("message_template_id") Long messageTemplateId,
      @JsonProperty("category_id") Long categoryId,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("from_date") Long fromDate,
      @JsonProperty("to_date") Long toDate,
      @JsonProperty("feature") CommunicationFeature feature,
      @JsonProperty("grade_slugs") List<String> gradeSlugs,
      @JsonProperty("teachers") List<Teacher> teachers) {}

  @Builder
  public record StudentNotificationResponse(
      @JsonProperty("notification_id") Long notificationId,
      String title,
      String type,
      @JsonProperty("teacher_name") String teacherName,
      String message,
      List<String> attachment,
      List<String> link,
      MessageTemplateCategory messageTemplateCategory,
      @JsonProperty("test_response") TestAttributes attributes,
      @JsonProperty("created_at") Long createdAt,
      @JsonProperty("from_date") Long fromDate,
      @JsonProperty("to_date") Long toDate) {}

  @Builder
  public record NotificationResponse(Long date, List<TeacherNotificationResponse> notifications) {}

  @Builder
  public record TeacherNotificationResponse(
      @JsonProperty("notification_id") Long notificationId,
      String title,
      String type,
      String message,
      List<String> attachment,
      List<String> link,
      MessageTemplateCategory messageTemplateCategory,
      @JsonProperty("classroom_ids") List<Long> classroomIds,
      @JsonProperty("classroom_name") List<String> classroomName,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("section_ids") List<String> sectionIds,
      @JsonProperty("section_name") List<String> sectionName,
      @JsonProperty("student_ids") List<Long> studentIds,
      @JsonProperty("teacher_ids") List<Long> teacherIds,
      @JsonProperty("teacher_names") String teacherNames,
      @JsonProperty("from_date") Long fromDate,
      @JsonProperty("to_date") Long toDate,
      @JsonProperty("feature") CommunicationFeature feature,
      @JsonProperty("colour") String colour,
      @JsonProperty("created_at") Long createdAt) {}

  @Builder
  public record TestMessage(String message, Long id) {}

  @Builder
  public record TestAttributes(
      String testName,
      Long testId,
      String testType,
      String subtopic,
      String subject,
      @JsonProperty("exam_ref") String examRef,
      @JsonProperty("test_definition_id") Long testDefinitionId,
      @JsonProperty("schedule_test_uuid") String testUuid) {}

  @Builder
  public record NotificationLogResponse(
      @JsonProperty("notification_id") Long notificationId,
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("student_name") String studentName,
      @JsonProperty("sms_status") Boolean smsStatus,
      @JsonProperty("whatsapp_status") Boolean whatsAppStatus,
      @JsonProperty("email_status") Boolean emailStatus,
      @JsonProperty("rejection_reason") String rejectionReason,
      @JsonProperty("mobile_number") Long mobileNumber,
      Boolean rejected) {}

  @Builder
  public record NotificationAttributes(
      @JsonProperty("notification_title") String title,
      @JsonProperty("teacher_name") String teacherName,
      @JsonProperty("notification_students") List<NotificationByIdResponse> notificationStudents) {}

  @Builder
  public record NotificationByIdResponse(
      @JsonProperty("student_name") String studentName,
      @JsonProperty("sms_status") NotificationMessageType smsStatus,
      @JsonProperty("whatsapp_status") NotificationMessageType whatsAppStatus,
      @JsonProperty("email_status") NotificationMessageType emailStatus,
      @JsonProperty("user_id") String userId,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("section_name") String sectionName,
      @JsonProperty("section_uuid") String sectionUuid) {}

  public record EmailRequest(
      @JsonProperty("message_template_id") Long messageTemplateId,
      String title,
      String message,
      @JsonProperty("attachment") String attachment,
      @JsonProperty("send_type") String sendType,
      @JsonProperty("scheduled_date_time") LocalDateTime scheduledDateTime,
      @JsonProperty("notification_type") @NotNull
          com.wexl.retail.notifications.model.NotificationType notificationType,
      SendTo sendTo,
      @JsonProperty("send_message") @NotNull List<Message> sendMessage) {}

  public record SendTo(
      @JsonProperty("groups") List<String> groups,
      Map<EmailRecipientGroup, List<Long>> individuals,
      ClassSelection classSelection) {}

  public record ClassSelection(
      @JsonProperty("section_uuids") List<String> sectionUuids,
      @JsonProperty("send_to") List<String> sendTo) {}

  @Builder
  public record EmailTo(String email, String name, String mobileNumber, String sectionName) {}

  @Builder
  public record StudentEmailRecipient(
      Long studentId, Long teacherId, NotificationDto.EmailTo emailTo) {}

  @Builder
  public record EmailNotifications(
      @JsonProperty("notifications")
          List<NotificationDto.TeacherNotificationResponse> notifications) {}
}
