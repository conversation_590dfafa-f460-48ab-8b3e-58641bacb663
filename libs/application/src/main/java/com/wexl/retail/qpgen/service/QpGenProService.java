package com.wexl.retail.qpgen.service;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.AuthUtil;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.ChapterResponse;
import com.wexl.retail.content.model.QuestionType;
import com.wexl.retail.model.User;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.qpgen.dto.*;
import com.wexl.retail.qpgen.model.BluePrint;
import com.wexl.retail.qpgen.model.InternalChoice;
import com.wexl.retail.qpgen.model.QPGenPro;
import com.wexl.retail.qpgen.repository.BluePrintSectionRepository;
import com.wexl.retail.qpgen.repository.InternalChoiceRepository;
import com.wexl.retail.qpgen.repository.QpGenProRepository;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.test.schedule.service.ScheduleTestService;
import com.wexl.retail.test.school.domain.*;
import com.wexl.retail.test.school.dto.*;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import com.wexl.retail.test.school.repository.TestDefinitionSectionRepository;
import com.wexl.retail.test.school.repository.TestQuestionRepository;
import com.wexl.retail.test.school.service.TestDefinitionService;
import com.wexl.retail.util.StrapiService;
import com.wexl.retail.util.ValidationUtils;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class QpGenProService {

  private final BluePrintService bluePrintService;
  private final QpGenProRepository qpGenProRepository;
  private final TestDefinitionRepository testDefinitionRepository;
  private final AuthService authService;
  private final ContentService contentService;
  private final ValidationUtils validationUtils;
  private final TestDefinitionService testDefinitionService;
  private final TestQuestionRepository testQuestionRepository;
  private final UserService userService;
  private final InternalChoiceRepository internalChoiceRepository;
  private final StrapiService strapiService;
  private final TestDefinitionSectionRepository testDefinitionSectionRepository;
  private final BluePrintSectionRepository bluePrintSectionRepository;
  private final ScheduleTestService scheduleTestService;

  @Value("${app.contentToken}")
  String contentBearerToken;

  @Transactional
  public QpGenProDto.Return saveQpGenPro(String orgSlug, QpGenProDto.Request request) {
    var chapters =
        contentService.getChaptersByBoardGradeAndSubject(
            orgSlug, request.boardSlug(), request.gradeSlug(), request.subjectSlug());
    TestDefinition testDefinition = createAndSaveTestDefinition(request, orgSlug, chapters);
    QPGenPro qpGen = buildAndSaveQpGenPro(orgSlug, request, chapters);
    return updateQpGen(qpGen, testDefinition);
  }

  private QPGenPro buildAndSaveQpGenPro(
      String orgSlug, QpGenProDto.Request request, List<ChapterResponse> chapters) {
    QPGenPro qpGen = buildQpGen(orgSlug, request, chapters);
    qpGen.setStatus(QpGenStatus.IN_PROGRESS);
    return qpGenProRepository.save(qpGen);
  }

  private QpGenProDto.Return updateQpGen(QPGenPro qpGen, TestDefinition testDefinition) {
    qpGen.setTestDefinitionId(testDefinition.getId());
    qpGen.setStatus(QpGenStatus.DRAFT);
    qpGen.setMarks(testDefinition.getTotalMarks().longValue());
    var qpGenPro = qpGenProRepository.save(qpGen);
    return QpGenProDto.Return.builder().id(qpGenPro.getId()).build();
  }

  private QPGenPro buildQpGen(
      String orgSlug, QpGenProDto.Request request, List<ChapterResponse> chapters) {
    BluePrint bluePrint = bluePrintService.validateBluePrint(orgSlug, request.bluePrintId());
    var chapterSlugs =
        request.chapterSlug() == null
            ? chapters.stream().map(ChapterResponse::getChapterSlug).toList()
            : request.chapterSlug();
    var chapterNames =
        request.chapterName() == null
            ? chapters.stream().map(ChapterResponse::getName).toList()
            : request.chapterName();
    return QPGenPro.builder()
        .marks(request.marks())
        .duration(request.duration())
        .orgSlug(orgSlug)
        .status(QpGenStatus.IN_PROGRESS)
        .title(request.title())
        .boardSlug(request.boardSlug())
        .boardName(request.boardName())
        .gradeName(request.gradeName())
        .gradeSlug(request.gradeSlug())
        .subjectSlug(request.subjectSlug())
        .subjectName(request.subjectName())
        .chapterSlug(chapterSlugs)
        .chapterName(chapterNames)
        .bluePrint(bluePrint)
        .createdByTeacher(authService.getUserDetails().getId())
        .build();
  }

  private TestDefinition createAndSaveTestDefinition(
      QpGenProDto.Request request, String orgSlug, List<ChapterResponse> chapters) {
    TestDefinition testDefinition = new TestDefinition();
    testDefinition.setBoardSlug(request.boardSlug());
    testDefinition.setGradeSlug(request.gradeSlug());
    testDefinition.setSubjectSlug(request.subjectSlug());
    testDefinition.setCategory(TestCategory.DEFAULT);
    testDefinition.setActive(Boolean.TRUE);
    testDefinition.setType(TestType.MOCK_TEST);
    testDefinition.setIsAutoEnabled(Boolean.TRUE);
    testDefinition.setOrganization(orgSlug);
    testDefinition.setTestName(request.title());
    testDefinition.setMessage(request.title());
    testDefinition.setMetadata(TestDefinitionMetadata.builder().build());
    testDefinition.setTestDefinitionSections(
        mergeDuplicateSectionsByName(
            buildTestDefinitionSections(request, orgSlug, testDefinition, chapters),
            testDefinition));
    testDefinition.setTeacher(authService.getTeacherDetails());
    testDefinition.setNoOfQuestions(0);
    testDefinition.setTotalMarks(request.marks().intValue());
    return testDefinitionRepository.save(testDefinition);
  }

  private List<TestDefinitionSection> mergeDuplicateSectionsByName(
      List<TestDefinitionSection> testDefinitionSections, TestDefinition testDefinition) {
    List<TestDefinitionSection> sectionsResponse = new ArrayList<>();
    var sectionNames =
        testDefinitionSections.stream().map(TestDefinitionSection::getName).distinct().toList();

    for (int i = 0; i < sectionNames.size(); i++) {
      String sectionName = sectionNames.get(i);
      List<TestDefinitionSection> sectionData =
          testDefinitionSections.stream()
              .filter(section -> section.getName().equals(sectionName))
              .toList();
      List<TestQuestion> questionList =
          sectionData.stream()
              .map(TestDefinitionSection::getTestQuestions)
              .flatMap(List::stream)
              .toList();

      TestDefinitionSection tds = new TestDefinitionSection();
      tds.setName(sectionName);
      tds.setNoOfQuestions((long) questionList.size());
      tds.setSequenceNumber(i + 1L);
      tds.setTestDefinition(testDefinition);
      tds.setTestQuestions(buildQuestionsData(questionList, tds));
      sectionsResponse.add(tds);
    }

    return sectionsResponse;
  }

  private List<TestQuestion> buildQuestionsData(
      List<TestQuestion> questionList, TestDefinitionSection tds) {
    return questionList.stream()
        .map(
            question ->
                TestQuestion.builder()
                    .marks(Math.toIntExact(question.getMarks()))
                    .questionUuid(question.getQuestionUuid())
                    .type(question.getType())
                    .testDefinitionSection(tds)
                    .chapterSlug(question.getChapterSlug())
                    .chapterName(question.getChapterName())
                    .complexity(question.getComplexity())
                    .category(question.getCategory())
                    .mcqAnswer(question.getMcqAnswer())
                    .natAnswer(question.getNatAnswer())
                    .yesNo(getTrueFalseAnswer(String.valueOf(question.getYesNo())))
                    .amcqAnswer(question.getAmcqAnswer())
                    .fbqAnswer(question.getFbqAnswer())
                    .subjectiveAnswer(question.getSubjectiveAnswer())
                    .build())
        .toList();
  }

  private List<TestDefinitionSection> buildTestDefinitionSections(
      QpGenProDto.Request request,
      String orgSlug,
      TestDefinition td,
      List<ChapterResponse> chapters) {
    var bluePrint = bluePrintService.validateBluePrint(orgSlug, request.bluePrintId());
    List<TestDefinitionSection> sections = new ArrayList<>();
    var chapterSlugs =
        request.chapterSlug() == null
            ? chapters.stream().map(ChapterResponse::getChapterSlug).toList()
            : request.chapterSlug();
    var contentData =
        contentService.getQuestionsForQpGenPro(
            bluePrintService.buildBluePrintSectionsResponse(bluePrint.getBluePrintSections()),
            orgSlug,
            chapterSlugs);
    bluePrint
        .getBluePrintSections()
        .forEach(
            section -> {
              var sectionData =
                  contentData.stream()
                      .filter(
                          x ->
                              section.getSectionName().equals(x.sectionName())
                                  && section.getId().equals(x.sectionId()))
                      .findFirst();
              if (sectionData.isEmpty()
                  || sectionData.get().questionsList().size() != section.getQuestionCount()) {

                throw new ApiException(
                    InternalErrorCodes.INVALID_REQUEST,
                    "error.QPGen.QuestionCount",
                    new String[] {section.getSectionName()});
              }
              TestDefinitionSection tds = new TestDefinitionSection();
              tds.setName(section.getSectionName());
              tds.setNoOfQuestions(section.getQuestionCount());
              tds.setSequenceNumber((long) bluePrint.getBluePrintSections().indexOf(section) + 1);
              tds.setTestQuestions(
                  sectionData
                      .map(
                          qpGenResponse ->
                              buildTestQuestions(
                                  qpGenResponse,
                                  tds,
                                  bluePrint.getBluePrintSections().get(0).getMarks()))
                      .orElse(null));
              tds.setTestDefinition(td);
              sections.add(tds);
            });
    return sections;
  }

  private List<TestQuestion> buildTestQuestions(
      QpGenDto.QpGenResponse sectionData, TestDefinitionSection tds, double sectionMarks) {
    List<TestQuestion> testQuestionList = new ArrayList<>();
    sectionData
        .questionsList()
        .forEach(
            question ->
                testQuestionList.add(
                    TestQuestion.builder()
                        .marks(Math.toIntExact((long) sectionMarks))
                        .questionUuid(question.uuid())
                        .type(question.type().toString())
                        .testDefinitionSection(tds)
                        .chapterSlug(question.chapterSlug())
                        .chapterName(question.chapterName())
                        .complexity(question.complexitySlug())
                        .category(question.categorySlug())
                        .mcqAnswer(
                            question.type().equals(QuestionType.MCQ)
                                ? Long.valueOf(question.answer())
                                : null)
                        .natAnswer(
                            question.type().equals(QuestionType.NAT)
                                ? Float.valueOf((question.answer()))
                                : null)
                        .yesNo(
                            question.type().equals(QuestionType.YESNO)
                                ? getTrueFalseAnswer(question.answer())
                                : null)
                        .amcqAnswer(
                            question.type().equals(QuestionType.AMCQ)
                                ? Integer.valueOf(question.answer())
                                : null)
                        .fbqAnswer(
                            question.type().equals(QuestionType.FBQ) ? question.answer() : null)
                        .subjectiveAnswer(
                            question.type().equals(QuestionType.SUBJECTIVE)
                                ? question.answer()
                                : null)
                        .build()));
    return testQuestionList;
  }

  private Boolean getTrueFalseAnswer(String answer) {
    return answer.equals("f") ? Boolean.FALSE : Boolean.TRUE;
  }

  public List<QpGenProDto.Response> getAllQpGenPros(String orgSlug) {
    Organization organization;
    organization = validationUtils.isOrgValid(orgSlug);
    User teacheUser = authService.getTeacherDetails();
    var qpGenProList = qpGenProRepository.findByOrgSlugOrderByIdDesc(organization.getSlug());
    if (AuthUtil.isOrgAdmin(teacheUser)) {
      return buildQpGenResponse(qpGenProList);
    }
    var teacherData =
        qpGenProList.stream()
            .filter(
                x ->
                    x.getCreatedByTeacher().equals(teacheUser.getId())
                        || (x.getReviewByTeacher() != null
                            && x.getReviewByTeacher().equals(teacheUser.getId())))
            .toList();
    return buildQpGenResponse(teacherData);
  }

  private List<QpGenProDto.Response> buildQpGenResponse(List<QPGenPro> qpGenList) {
    List<QpGenProDto.Response> responseList = new ArrayList<>();
    qpGenList.forEach(
        qpGen -> {
          var checkMetadata =
              qpGen.getMetaData() != null
                  && qpGen.getMetaData().subjectMetadata() != null
                  && !qpGen.getMetaData().subjectMetadata().isEmpty();
          var createdUser = userService.findUserById(qpGen.getCreatedByTeacher());
          if (qpGen.getTestDefinitionId() == null) {
            return;
          }
          TestDefinition testDefinition =
              validationUtils.validateTestDefinition(qpGen.getTestDefinitionId());
          var reviewer =
              qpGen.getReviewByTeacher() == null
                  ? null
                  : userService.findUserById(qpGen.getReviewByTeacher());
          var qpGenPresent =
              testDefinition.getType().equals(TestType.MOCK_TEST) ? Boolean.TRUE : Boolean.FALSE;
          var testDefResponse =
              TestDefinitionsDto.TestDefinitionResponse.builder()
                  .testDefId(testDefinition.getId())
                  .testType(testDefinition.getType().name())
                  .name(testDefinition.getTestName())
                  .isQpGenPresent(qpGenPresent)
                  .build();
          responseList.add(
              QpGenProDto.Response.builder()
                  .id(qpGen.getId())
                  .marks(
                      testDefinition.getTestDefinitionSections().stream()
                          .flatMap(section -> section.getTestQuestions().stream())
                          .mapToLong(TestQuestion::getMarks)
                          .sum())
                  .title(qpGen.getTitle())
                  .subjectName(qpGen.getSubjectName())
                  .gradeName(qpGen.getGradeName())
                  .gradeSlug(qpGen.getGradeSlug())
                  .boardSlug(qpGen.getBoardName())
                  .duration(qpGen.getDuration())
                  .status(qpGen.getStatus())
                  .listOfSubjects(
                      checkMetadata
                          ? qpGen.getMetaData().subjectMetadata().stream()
                              .map(QPGenProV2Dto.SubjectMetadata::subjectName)
                              .collect(Collectors.joining(","))
                          : null)
                  .testDefinitionResponse(testDefResponse)
                  .createdByName(createdUser.getFirstName() + " " + createdUser.getLastName())
                  .createdBy(createdUser.getAuthUserId())
                  .reviewerName(
                      reviewer == null ? null : reviewer.getFirstName() + reviewer.getLastName())
                  .reviewerAuthId(reviewer == null ? null : reviewer.getAuthUserId())
                  .createdAt(
                      DateTimeUtil.convertIso8601ToEpoch(qpGen.getCreatedAt().toLocalDateTime()))
                  .isDummyQuestionsExist(checkIfDummyQuestionsExist(testDefinition))
                  .build());
        });

    return responseList;
  }

  private Boolean checkIfDummyQuestionsExist(TestDefinition testDefinition) {
    return testDefinition.getTestDefinitionSections().stream()
        .flatMap(section -> section.getTestQuestions().stream())
        .anyMatch(question -> "dummy_question".equals(question.getCategory()));
  }

  public QpGenProDto.Response getQpGenProById(Long qpGenProId) {
    var qpGenPro = validateQpGenPro(qpGenProId);
    var testDefinitionId = qpGenPro.getTestDefinitionId();
    if (testDefinitionId == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Invalid.TestDefinition ");
    }
    var testDefinitionResponse = testDefinitionService.getTestDefinitionByIdV1(testDefinitionId);
    return buildResponse(qpGenPro, testDefinitionResponse);
  }

  private QpGenProDto.Response buildResponse(
      QPGenPro qpGen, TestDefinitionsDto.TestDefinitionResponse testDefinitionResponse) {
    var bluePrintSections =
        bluePrintSectionRepository.getBpSectionsByTestDefinitionId(
            testDefinitionResponse.testDefId());
    List<QpGenProDto.BluePrintSectionDetails> bpSectionDetails = new ArrayList<>();
    bluePrintSections.forEach(
        bps ->
            bpSectionDetails.add(
                new QpGenProDto.BluePrintSectionDetails(
                    bps.getSectionName(), (long) bps.getMarks())));
    return QpGenProDto.Response.builder()
        .id(qpGen.getId())
        .marks(qpGen.getMarks())
        .title(qpGen.getTitle())
        .subjectName(qpGen.getSubjectName())
        .subjectSlug(qpGen.getSubjectSlug())
        .listOfSubjects(
            qpGen.getMetaData().subjectMetadata().stream()
                .map(QPGenProV2Dto.SubjectMetadata::subjectName)
                .collect(Collectors.joining(",")))
        .gradeName(qpGen.getGradeName())
        .chapterSlug(qpGen.getChapterSlug())
        .gradeSlug(qpGen.getGradeSlug())
        .bluePrintId(qpGen.getBluePrint().getId())
        .duration(qpGen.getDuration())
        .createdAt(DateTimeUtil.convertIso8601ToEpoch(qpGen.getCreatedAt().toLocalDateTime()))
        .testDefinitionResponse(testDefinitionResponse)
        .bluePrintSectionDetails(bpSectionDetails)
        .build();
  }

  public QPGenPro validateQpGenPro(Long qpGenProId) {
    var qpGenPro = qpGenProRepository.findById(qpGenProId);
    if (qpGenPro.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.Invalid.QpGenProId",
          new String[] {qpGenProId.toString()});
    }
    return qpGenPro.get();
  }

  public void updateMarks(Long qpGenProId, QpGenProDto.ChangeMarks request) {
    TestQuestion testQuestion =
        validateAndGetTestQuestion(
            qpGenProId, request.testDefinitionId(), request.sectionId(), request.QuestionUuid());
    testQuestion.setMarks(request.marks().intValue());
    testQuestionRepository.save(testQuestion);
    TestDefinition testDefinition =
        testDefinitionRepository.findById(request.testDefinitionId()).get();
    Long updatedMarks =
        testDefinition.getTestDefinitionSections().stream()
            .flatMap(section -> section.getTestQuestions().stream())
            .mapToLong(TestQuestion::getMarks)
            .sum();
    testDefinition.setTotalMarks(Integer.valueOf(updatedMarks.intValue()));
    testDefinitionRepository.save(testDefinition);
    QPGenPro qpGenPro = qpGenProRepository.findById(qpGenProId).get();
    qpGenPro.setMarks(updatedMarks);
    qpGenProRepository.save(qpGenPro);
  }

  public void replaceQuestion(
      Long qpGenProId, QpGenProDto.ReplaceQuestion request, String orgSlug) {
    TestQuestion testQuestion =
        validateAndGetTestQuestion(
            qpGenProId, request.testDefinitionId(), request.sectionId(), request.QuestionUuid());
    var qpGenBpSections =
        bluePrintSectionRepository.getBpSectionsByTestDefinitionId(request.testDefinitionId());
    var testDefSection =
        testDefinitionSectionRepository.findById(request.sectionId()).orElseThrow();
    var bpSection =
        qpGenBpSections.stream()
            .filter(section -> section.getSectionName().equals(testDefSection.getName()))
            .findFirst()
            .orElseThrow();
    var chapter = contentService.getChapterBySlug(request.question().chapterSlug());
    testQuestion.setMarks((int) bpSection.getMarks());
    testQuestion.setQuestionUuid(request.question().uuid());
    testQuestion.setType(request.question().type().name());
    testQuestion.setComplexity(request.question().complexity().toLowerCase());
    testQuestion.setChapterSlug(chapter.getChapterSlug());
    testQuestion.setChapterName(chapter.getName());
    testQuestion.setMcqAnswer(
        request.question().type().equals(QuestionType.MCQ)
            ? Long.valueOf(request.question().answer())
            : null);
    testQuestion.setNatAnswer(
        request.question().type().equals(QuestionType.NAT)
            ? Float.valueOf(request.question().answer())
            : null);
    testQuestion.setYesNo(
        request.question().type().equals(QuestionType.YESNO)
            ? getTrueFalseAnswer(request.question().answer())
            : null);
    testQuestion.setAmcqAnswer(
        request.question().type().equals(QuestionType.AMCQ)
            ? Integer.valueOf(request.question().answer())
            : null);
    testQuestion.setFbqAnswer(
        request.question().type().equals(QuestionType.FBQ) ? request.question().answer() : null);
    testQuestion.setSubjectiveAnswer(
        request.question().type().equals(QuestionType.SUBJECTIVE)
            ? request.question().answer()
            : null);
    testQuestionRepository.save(testQuestion);
    refreshSectionSummary(qpGenProId, request, orgSlug);
    updateInternalChoice(request.sectionId(), request.QuestionUuid(), request.question().uuid());
  }

  private void refreshSectionSummary(
      Long qpGenProId, QpGenProDto.ReplaceQuestion request, String orgSlug) {
    var qpGenPro = validateQpGenPro(qpGenProId);

    List<QPGenProV2Dto.QuestionSummaryResponse> questionSummaryResponses = new ArrayList<>();
    var testDefinitionData =
        testDefinitionRepository.getTestDefinitionSummary(request.testDefinitionId());
    var categories = contentService.getQuestionCategories(orgSlug);
    var complexities = contentService.getQuestionComplexities(orgSlug);
    var sectionIds =
        testDefinitionData.stream().map(TestDefinitionSummary::getSectionId).distinct().toList();

    sectionIds.forEach(
        sectionId -> {
          var sectionData =
              testDefinitionData.stream()
                  .filter(data -> data.getSectionId().equals(sectionId))
                  .toList();
          if (!sectionData.isEmpty()) {
            var section = sectionData.get(0);
            questionSummaryResponses.add(
                QPGenProV2Dto.QuestionSummaryResponse.builder()
                    .sectionName(section.getSectionName())
                    .sectionsResponses(
                        buildSectionSummary(
                            sectionData,
                            categories,
                            complexities,
                            qpGenPro.getSummary(),
                            request,
                            orgSlug))
                    .build());
          }
        });

    if (!questionSummaryResponses.isEmpty()) {
      qpGenPro.setSummary(questionSummaryResponses);
      qpGenProRepository.save(qpGenPro);
    }
  }

  private List<QPGenProV2Dto.SectionsResponse> buildSectionSummary(
      List<TestDefinitionSummary> sectionData,
      List<QPGenProV2Dto.QuestionCategory> categories,
      List<QPGenProV2Dto.QuestionComplexities> complexities,
      List<QPGenProV2Dto.QuestionSummaryResponse> qpGenProSummary,
      QpGenProDto.ReplaceQuestion request,
      String orgSlug) {

    return sectionData.stream()
        .map(
            data ->
                buildSectionResponse(
                    data, categories, complexities, qpGenProSummary, request, orgSlug))
        .toList();
  }

  private QPGenProV2Dto.SectionsResponse buildSectionResponse(
      TestDefinitionSummary data,
      List<QPGenProV2Dto.QuestionCategory> categories,
      List<QPGenProV2Dto.QuestionComplexities> complexities,
      List<QPGenProV2Dto.QuestionSummaryResponse> qpGenProSummary,
      QpGenProDto.ReplaceQuestion request,
      String orgSlug) {

    String complexityName = getComplexityName(data.getComplexity(), complexities);
    String categoryName = getCategoryName(data.getCategory(), categories);
    Optional<QPGenProV2Dto.SectionsResponse> existingSummary =
        getExistingSummary(data, qpGenProSummary);

    TestDefinition testDefinition =
        validationUtils.validateTestDefinition(request.testDefinitionId());

    QuestionDto.SectionQpGenRequest sectionRequest =
        buildSectionRequest(data, complexityName, categoryName, existingSummary, testDefinition);
    var chapterResponse = buildChapterResponse(sectionRequest, orgSlug);

    return buildSectionsResponse(
        data, complexityName, categoryName, request, existingSummary, chapterResponse);
  }

  private String getComplexityName(
      String complexitySlug, List<QPGenProV2Dto.QuestionComplexities> complexities) {
    return complexities.stream()
        .filter(complexity -> complexity.slug().equals(complexitySlug))
        .map(QPGenProV2Dto.QuestionComplexities::name)
        .findFirst()
        .orElse(complexitySlug);
  }

  private String getCategoryName(
      String categorySlug, List<QPGenProV2Dto.QuestionCategory> categories) {
    return categories.stream()
        .filter(category -> category.slug().equals(categorySlug))
        .map(QPGenProV2Dto.QuestionCategory::name)
        .findFirst()
        .orElse(categorySlug);
  }

  private Optional<QPGenProV2Dto.SectionsResponse> getExistingSummary(
      TestDefinitionSummary data, List<QPGenProV2Dto.QuestionSummaryResponse> qpGenProSummary) {
    return qpGenProSummary.stream()
        .filter(summary -> summary.sectionName().equals(data.getSectionName()))
        .flatMap(
            summary ->
                summary.sectionsResponses().stream()
                    .filter(
                        response ->
                            response.questionType().equals(data.getType())
                                && response.questionComplexitySlug().equals(data.getComplexity())
                                && response.questionTagSlug().equals(data.getCategory())))
        .findFirst();
  }

  private QuestionDto.SectionQpGenRequest buildSectionRequest(
      TestDefinitionSummary data,
      String complexityName,
      String categoryName,
      Optional<QPGenProV2Dto.SectionsResponse> existingSummary,
      TestDefinition testDefinition) {

    return QuestionDto.SectionQpGenRequest.builder()
        .chapterName(data.getChapterName())
        .chapterSlug(data.getChapterSlug())
        .sectionName(
            existingSummary
                .map(QPGenProV2Dto.SectionsResponse::sectionName)
                .orElse(data.getSectionName()))
        .complexity(data.getComplexity())
        .complexityName(complexityName)
        .questionCategory(data.getCategory())
        .questionCategoryName(categoryName)
        .questionType(data.getType())
        .subjectSlug(testDefinition.getSubjectSlug())
        .build();
  }

  private QPGenProV2Dto.SectionsResponse buildSectionsResponse(
      TestDefinitionSummary data,
      String complexityName,
      String categoryName,
      QpGenProDto.ReplaceQuestion request,
      Optional<QPGenProV2Dto.SectionsResponse> existingSummary,
      QuestionDto.QpGenChapterQuestions chapterResponse) {

    return QPGenProV2Dto.SectionsResponse.builder()
        .sectionId(Long.valueOf(data.getSectionId()))
        .chapterSlug(data.getChapterSlug())
        .sectionName(data.getSectionName())
        .questionComplexityName(complexityName)
        .questionComplexitySlug(data.getComplexity())
        .questionTagSlug(categoryName)
        .questionType(data.getType())
        .totalAvailableQuestions(
            existingSummary
                .map(QPGenProV2Dto.SectionsResponse::totalAvailableQuestions)
                .orElse(null))
        .build();
  }

  private Long getTotalQuestions(
      Long requestedMarks,
      int markType,
      Long chapterResponseValue,
      Optional<Long> existingSummaryValue) {
    return requestedMarks == markType ? chapterResponseValue : existingSummaryValue.orElse(null);
  }

  private String getCategory(String category, String orgSlug) {
    var questionCategories = contentService.getQuestionCategories(orgSlug);
    var categorySlug = questionCategories.stream().filter(x -> x.name().equals(category)).findAny();
    if (categorySlug.isEmpty()) {
      return category;
    }
    return categorySlug.get().slug();
  }

  private void updateInternalChoice(
      Long sectionId, String oldQuestionUuid, String newQuestionUuid) {
    var optionalInternalChoice =
        internalChoiceRepository.findByTestSectionIdAndTestQuestionUUid(sectionId, oldQuestionUuid);
    if (optionalInternalChoice.isEmpty()) {
      return;
    }
    var internalChoice = optionalInternalChoice.get();
    internalChoice.setTestQuestionUUid(newQuestionUuid);
    internalChoiceRepository.save(internalChoice);
  }

  private TestQuestion validateAndGetTestQuestion(
      Long qpGenProId, Long testDefinitionId, Long sectionId, String questionUuid) {
    validateQpGenPro(qpGenProId);
    TestDefinition testDefinition = validationUtils.validateTestDefinition(testDefinitionId);

    TestDefinitionSection testDefinitionSection =
        testDefinition.getTestDefinitionSections().stream()
            .filter(x -> x.getId().equals(sectionId))
            .findFirst()
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST, "Invalid TestDefinitionSection"));

    return testDefinitionSection.getTestQuestions().stream()
        .filter(x -> x.getQuestionUuid().equals(questionUuid))
        .findFirst()
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST, "error.Invalid.Question.Uuid"));
  }

  public void reviewQpGenPro(Long qpGenProId, String authUserId) {
    var qpGen = validateQpGenPro(qpGenProId);
    var user = validationUtils.isValidUser(authUserId);
    qpGen.setReviewByTeacher(user.getId());
    qpGen.setStatus(QpGenStatus.PENDING_REVIEW);
    qpGenProRepository.save(qpGen);
  }

  public void publishQpGenPro(Long qpGenProId, String bearerToken, Boolean publishStatus) {
    var qpGen = validateQpGenPro(qpGenProId);

    if (qpGen.getReviewByTeacher() == null
        || (!qpGen.getReviewByTeacher().equals(authService.getUserDetails().getId()))) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.unAuthorisedToPublish");
    }
    testDefinitionService.publishTestDefinitionById(
        qpGen.getTestDefinitionId(), publishStatus, bearerToken, true);

    qpGen.setStatus(
        Boolean.TRUE.equals(publishStatus) ? QpGenStatus.PUBLISHED : QpGenStatus.PENDING_REVIEW);

    qpGenProRepository.save(qpGen);
  }

  public void internalChoice(Long qpGenProId, QpGenProDto.InternalChoice request) {
    var qpGen = validateQpGenPro(qpGenProId);
    TestDefinition testDefinition =
        validationUtils.validateTestDefinition(qpGen.getTestDefinitionId());

    TestDefinitionSection testDefinitionSection =
        findTestDefinitionSection(testDefinition, request.sectionId());
    var testQuestion = findTestQuestion(testDefinitionSection, request);

    var internalChoice = findInternalChoice(testQuestion);

    var question = request.testQuestionRequest();
    if (internalChoice != null) {
      updateInternalChoice(internalChoice, testQuestion, question);
    } else {
      createNewInternalChoice(testQuestion, question);
    }
  }

  private TestDefinitionSection findTestDefinitionSection(
      TestDefinition testDefinition, Long sectionId) {
    return testDefinition.getTestDefinitionSections().stream()
        .filter(x -> x.getId().equals(sectionId))
        .findFirst()
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST, "Invalid TestDefinitionSection"));
  }

  private TestQuestion findTestQuestion(
      TestDefinitionSection testDefinitionSection, QpGenProDto.InternalChoice request) {
    return testQuestionRepository.findByTestDefinitionSectionAndQuestionUuid(
        testDefinitionSection, request.questionUuid());
  }

  private InternalChoice findInternalChoice(TestQuestion testQuestion) {
    return internalChoiceRepository.findByTestSectionIdAndTestQuestionUUidAndTestQuestionId(
        testQuestion.getTestDefinitionSection().getId(),
        testQuestion.getQuestionUuid(),
        testQuestion.getId());
  }

  private void updateInternalChoice(
      InternalChoice internalChoice, TestQuestion testQuestion, TestQuestionRequest question) {
    internalChoice.setMarks(testQuestion.getMarks());
    internalChoice.setQuestionUuid(question.getQuestionUuid());
    internalChoice.setType(question.getType());
    internalChoice.setMcqAnswer(null);
    internalChoice.setSubjectiveAnswer(null);
    internalChoice.setTestQuestionUUid(testQuestion.getQuestionUuid());
    internalChoice.setMcqAnswer(testQuestion.getMcqAnswer());
    internalChoice.setSubjectiveAnswer(testQuestion.getSubjectiveAnswer());
    internalChoice.setSubjectSlug(question.getSubjectSlug());
    internalChoice.setSubtopicSlug(question.getSubtopicSlug());
    internalChoice.setChapterSlug(question.getChapterSlug());
    testQuestionRepository.save(testQuestion);
  }

  private void createNewInternalChoice(TestQuestion testQuestion, TestQuestionRequest question) {

    var internalChoice =
        InternalChoice.builder()
            .questionUuid(question.getQuestionUuid())
            .type(question.getType())
            .subtopicSlug(question.getSubtopicSlug())
            .testQuestionId(testQuestion.getId())
            .testQuestionUUid(testQuestion.getQuestionUuid())
            .testSectionId(testQuestion.getTestDefinitionSection().getId())
            .mcqAnswer(question.getMcqAnswer())
            .subjectiveAnswer(question.getAnswer())
            .subjectSlug(question.getSubjectSlug())
            .chapterSlug(question.getChapterSlug())
            .build();
    internalChoiceRepository.save(internalChoice);
  }

  public TestDefinitionResponse getQuestionsByTestDefinitionSectionId(
      long testDefinitionSectionId, String bearerToken, String orgSlug) {
    var testDefinitionResponse =
        testDefinitionService.getQuestionsByTestDefinitionSectionId(
            testDefinitionSectionId, bearerToken, orgSlug);

    var testQuestions = testDefinitionResponse.getQuestionsList();

    List<QuestionDto.Question> questionsList = new ArrayList<>();
    testQuestions.forEach(
        question ->
            questionsList.add(
                testDefinitionService.buildQuestions(question, testDefinitionSectionId, orgSlug)));
    testDefinitionResponse.setQuestionsList(questionsList);
    return testDefinitionResponse;
  }

  public void deleteSectionQuestion(long testDefinitionSectionId, String questionUuid) {
    var testDefinitionSection =
        testDefinitionService.getTestDefinitionSection(testDefinitionSectionId);
    var testQuestions = testDefinitionSection.getTestQuestions();
    if (testQuestions == null || testQuestions.isEmpty()) {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "error.sectionQuestionNotFound");
    }
    boolean questionExists =
        testQuestions.stream()
            .anyMatch(testQuestion -> questionUuid.equals(testQuestion.getQuestionUuid()));
    if (questionExists) {
      testQuestionRepository.deleteQuestionBySectionAndUuid(questionUuid, testDefinitionSectionId);
    } else {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "error.questionUuidNotFound");
    }
  }

  public QuestionDto.QuestionResponse getQpGenProQuestions(
      Long testDefinitionId, Integer questionSetNo, String orgSlug) {
    var questions =
        testDefinitionService.getTestDefinitionQuestions(testDefinitionId, questionSetNo);
    return buildQuestionResponse(questions, orgSlug);
  }

  public QuestionDto.QuestionResponse buildQuestionResponse(
      QuestionDto.QuestionResponse questions, String orgSlug) {
    Optional<QPGenPro> data =
        qpGenProRepository.findByTestDefinitionId(questions.testDefinitionId());

    Long duration = data.map(QPGenPro::getDuration).orElse(null);
    var testDefinition = validationUtils.validateTestDefinition(questions.testDefinitionId());
    var subjectName = strapiService.getSubjectNameBySlug(testDefinition.getSubjectSlug());
    sortTestDefinitionSections(questions.testDefinitionSectionResponses());
    return QuestionDto.QuestionResponse.builder()
        .testDefinitionId(questions.testDefinitionId())
        .testName(questions.testName())
        .duration(duration)
        .noOfQuestions(questions.noOfQuestions())
        .subjectName(subjectName)
        .gradeName(questions.gradeName())
        .testDefinitionSectionResponses(
            buildSectionResponse(questions.testDefinitionSectionResponses(), orgSlug))
        .totalMarks(testDefinition.getTotalMarks())
        .assetSlug(
            Objects.nonNull(testDefinition.getMetadata())
                ? testDefinition.getMetadata().getAssetSlug()
                : null)
        .answerKey(testDefinitionService.getTestDefinitionAnswers(questions))
        .build();
  }

  private List<QuestionDto.TestDefinitionSectionResponse> buildSectionResponse(
      List<QuestionDto.TestDefinitionSectionResponse> testDefinitionSectionResponses,
      String orgSlug) {
    List<QuestionDto.TestDefinitionSectionResponse> responseList = new ArrayList<>();
    var testDefinitionSection =
        testDefinitionSectionRepository.findById(testDefinitionSectionResponses.getFirst().id());
    var testDefinition = testDefinitionSection.get().getTestDefinition();
    var qpGen = qpGenProRepository.findByTestDefinitionId(testDefinition.getId());
    var bluePrint = qpGen.get().getBluePrint();
    var bluePrintSections =
        bluePrintSectionRepository.findAllByBluePrintOrderBySectionName(bluePrint);
    testDefinitionSectionResponses.forEach(
        section -> {
          var bluePrintSection =
              bluePrintSections.stream()
                  .filter(sec -> section.name().equalsIgnoreCase(sec.getSectionName()))
                  .toList();

          responseList.add(
              QuestionDto.TestDefinitionSectionResponse.builder()
                  .id(section.id())
                  .noOfQuestions(section.noOfQuestions())
                  .seqNo(section.seqNo())
                  .marks(section.marks())
                  .name(section.name())
                  .questions(buildQpGenQuestions(section.questions(), section.id(), orgSlug))
                  .instructions(bluePrintSection.getFirst().getInstructions())
                  .build());
        });
    return responseList;
  }

  private List<QuestionDto.Question> buildQpGenQuestions(
      List<QuestionDto.Question> questions, Long sectionId, String orgSlug) {
    List<QuestionDto.Question> questionList = new ArrayList<>();
    questions.forEach(
        question ->
            questionList.add(testDefinitionService.buildQuestions(question, sectionId, orgSlug)));
    return questionList;
  }

  private void sortTestDefinitionSections(
      List<QuestionDto.TestDefinitionSectionResponse> testDefinitionSectionResponses) {
    testDefinitionSectionResponses.sort(
        (o1, o2) -> {
          final Long seq = o1.seqNo() - o2.seqNo();
          return seq.intValue();
        });
  }

  public void updateInternalChoiceMarks(
      Long qpGenProId, QpGenProDto.InternalChoiceChangeMarks request) {
    validateQpGenPro(qpGenProId);
    var internalQuestion = validateInternalChoice(request);
    internalQuestion.setMarks(request.marks());
    internalChoiceRepository.save(internalQuestion);
  }

  private InternalChoice validateInternalChoice(QpGenProDto.InternalChoiceChangeMarks request) {
    var optionalInternalQuestion =
        internalChoiceRepository.findByTestSectionIdAndTestQuestionUUidAndQuestionUuid(
            request.sectionId(), request.testQuestionUuid(), request.questionUuid());
    if (optionalInternalQuestion.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Invalid.InternalQuestion");
    }
    return optionalInternalQuestion.get();
  }

  public QuestionDto.SectionQpGenResponse getSectionQuestions(
      QuestionDto.SectionQpGenRequest chapterRequest, String orgSlug) {

    return QuestionDto.SectionQpGenResponse.builder()
        .chapterQuestions(buildChapterResponse(chapterRequest, orgSlug))
        .build();
  }

  private QuestionDto.QpGenChapterQuestions buildChapterResponse(
      QuestionDto.SectionQpGenRequest chapterRequest, String orgSlug) {
    long selectedChapterQuestionCount = 0L;
    long twoMarkQuestions = 0L;
    long oneMarkQuestions = 0L;
    long threeMarkQuestions = 0L;
    long fourMarkQuestions = 0L;
    long fiveMarkQuestions = 0L;
    Optional<TestDefinitionSection> testDefinitionSection = Optional.empty();

    if (chapterRequest.sectionId() != null) {
      testDefinitionSection = testDefinitionSectionRepository.findById(chapterRequest.sectionId());
      var selectedChapterQuestions =
          testDefinitionSection.get().getTestQuestions().stream()
              .filter(
                  qus ->
                      qus.getChapterSlug().equals(chapterRequest.chapterSlug())
                          && qus.getType().equals(chapterRequest.questionType())
                          && qus.getCategory().equals(chapterRequest.questionCategoryName())
                          && qus.getComplexity().equals(chapterRequest.complexityName()))
              .toList();
      selectedChapterQuestionCount = selectedChapterQuestions.size();

      Map<Integer, Long> markQuestionCount =
          selectedChapterQuestions.stream()
              .collect(Collectors.groupingBy(TestQuestion::getMarks, Collectors.counting()));
      oneMarkQuestions = markQuestionCount.getOrDefault(1, 0L);
      twoMarkQuestions = markQuestionCount.getOrDefault(2, 0L);
      threeMarkQuestions = markQuestionCount.getOrDefault(3, 0L);
      fourMarkQuestions = markQuestionCount.getOrDefault(4, 0L);
      fiveMarkQuestions = markQuestionCount.getOrDefault(5, 0L);
    }

    long total1mQuestions = 0;
    long total2mQuestions = 0;
    long total3mQuestions = 0;
    long total4mQuestions = 0;
    long total5mQuestions = 0;

    var contentData =
        contentService.getQuestionsByChapter(buildContentRequest(chapterRequest), orgSlug);
    var totalQuestions = contentData.size();
    if (totalQuestions > 0) {
      Map<Long, List<ContentQuestionsResponse>> groupedByMarks =
          contentData.stream().collect(Collectors.groupingBy(ContentQuestionsResponse::getMarks));
      total1mQuestions = groupedByMarks.getOrDefault(1L, List.of()).size();
      total2mQuestions = groupedByMarks.getOrDefault(2L, List.of()).size();
      total3mQuestions = groupedByMarks.getOrDefault(3L, List.of()).size();
      total4mQuestions = groupedByMarks.getOrDefault(4L, List.of()).size();
      total5mQuestions = groupedByMarks.getOrDefault(5L, List.of()).size();
    }

    return QuestionDto.QpGenChapterQuestions.builder()
        .chapterName(chapterRequest.chapterName())
        .chapterSlug(chapterRequest.chapterSlug())
        .questionType(chapterRequest.questionType())
        .questionCategoryName(chapterRequest.questionCategoryName())
        .questionCategorySlug(chapterRequest.questionCategory())
        .questionComplexityName(chapterRequest.complexityName())
        .questionComplexitySlug(chapterRequest.complexity())
        .totalAvailableQuestions((long) totalQuestions)
        .selectedChapterQuestions(selectedChapterQuestionCount)
        .total1mQuestions(total1mQuestions)
        .selected1mQuestions(oneMarkQuestions)
        .total2mQuestions(total2mQuestions)
        .selected2mQuestions(twoMarkQuestions)
        .total3mQuestions(total3mQuestions)
        .selected3mQuestions(threeMarkQuestions)
        .total4mQuestions(total4mQuestions)
        .selected4mQuestions(fourMarkQuestions)
        .total5mQuestions(total5mQuestions)
        .selected5mQuestions(fiveMarkQuestions)
        .testDefinitionSectionId(
            testDefinitionSection.map(TestDefinitionSection::getId).orElse(null))
        .build();
  }

  private BluePrintDto.ContentRequest buildContentRequest(
      QuestionDto.SectionQpGenRequest chapterRequest) {

    return BluePrintDto.ContentRequest.builder()
        .subjectSlug(chapterRequest.subjectSlug())
        .chapterName(chapterRequest.chapterName())
        .chapterSlug(chapterRequest.chapterSlug())
        .questionType(chapterRequest.questionType())
        .questionCategory(chapterRequest.questionCategory())
        .complexity(chapterRequest.complexity())
        .build();
  }

  public void updateSectionQuestions(
      String orgSlug,
      Long qpGenProId,
      Long testDefinitionSectionId,
      QpGenProDto.UpdateSectionQuestions updateSectionQuestions) {

    QPGenPro qpGenPro;
    TestDefinition testDefinition;
    List<TestQuestion> testQuestionList;
    qpGenPro = validateQpGenPro(qpGenProId);
    testDefinition =
        testDefinitionService.getTestDefinitionByIdAndOrgSlug(
            qpGenPro.getTestDefinitionId(), qpGenPro.getOrgSlug());
    var testDefinitionSection =
        testDefinition.getTestDefinitionSections().stream()
            .filter(tds -> tds.getId().equals(testDefinitionSectionId))
            .findFirst();

    if (testDefinitionSection.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.NO_RECORD_FOUND,
          "error.TestDefinitionSection",
          new String[] {Long.toString(testDefinitionSectionId)});
    }

    testQuestionList =
        testDefinitionSection.get().getTestQuestions().stream()
            .filter(
                tq ->
                    updateSectionQuestions.chapterSlug().equals(tq.getChapterSlug())
                        && updateSectionQuestions.questionType().equals(tq.getType())
                        && updateSectionQuestions.questionCategory().equals(tq.getCategory())
                        && updateSectionQuestions.questionComplexity().equals(tq.getComplexity()))
            .toList();

    for (QpGenProDto.QuestionsInfo questionsInfo : updateSectionQuestions.questions()) {
      if (questionsInfo.selectedQuestions() != null && questionsInfo.selectedQuestions() > 0) {

        List<TestQuestion> filteredTestQuestionsList;
        filteredTestQuestionsList =
            testQuestionList.stream()
                .filter(tq -> tq.getMarks().equals(Integer.parseInt(questionsInfo.marks())))
                .toList();

        if (filteredTestQuestionsList.isEmpty()
            || questionsInfo.selectedQuestions() > filteredTestQuestionsList.size()) {

          addNewQuestions(
              questionsInfo,
              testDefinitionSection.get(),
              updateSectionQuestions,
              orgSlug,
              filteredTestQuestionsList);

        } else {
          List<TestQuestion> questionsToDelete;
          questionsToDelete =
              filteredTestQuestionsList.subList(
                  0, (int) (filteredTestQuestionsList.size() - questionsInfo.selectedQuestions()));

          List<String> questionUuids;
          questionUuids = questionsToDelete.stream().map(TestQuestion::getQuestionUuid).toList();

          questionUuids.forEach(
              uuid ->
                  testDefinitionService.deleteQuestionByTestDefinitionSectionById(
                      testDefinitionSection.get().getId(), uuid));
        }
        testDefinitionSection
            .get()
            .setNoOfQuestions(
                testQuestionRepository.countByTestDefinitionSection(testDefinitionSection.get()));
        testDefinitionSectionRepository.save(testDefinitionSection.get());
        updateSectionSummary(qpGenPro, updateSectionQuestions, testDefinitionSection.get());
      }
    }
  }

  private void updateSectionSummary(
      QPGenPro qpGenPro,
      QpGenProDto.UpdateSectionQuestions updateSectionQuestions,
      TestDefinitionSection testDefinitionSection) {
    List<QPGenProV2Dto.QuestionSummaryResponse> summaryResponseList =
        qpGenPro.getSummary().stream()
            .map(
                summary -> {
                  boolean isSectionNameMatching =
                      Objects.equals(testDefinitionSection.getName(), summary.sectionName());
                  List<QPGenProV2Dto.SectionsResponse> sectionsResponses =
                      isSectionNameMatching
                          ? buildUpdateSectionResponse(
                              summary.sectionsResponses(), updateSectionQuestions)
                          : summary.sectionsResponses();

                  return QPGenProV2Dto.QuestionSummaryResponse.builder()
                      .sectionName(summary.sectionName())
                      .sectionsResponses(sectionsResponses)
                      .build();
                })
            .toList();

    qpGenPro.setSummary(summaryResponseList);
    qpGenProRepository.save(qpGenPro);
  }

  private List<QPGenProV2Dto.SectionsResponse> buildUpdateSectionResponse(
      List<QPGenProV2Dto.SectionsResponse> sectionsResponses,
      QpGenProDto.UpdateSectionQuestions updateSectionQuestions) {

    List<QPGenProV2Dto.SectionsResponse> sectionsResponseList = new ArrayList<>();
    boolean isUpdated = false;

    for (QPGenProV2Dto.SectionsResponse section : sectionsResponses) {
      if (section.chapterSlug().equals(updateSectionQuestions.chapterSlug())
          && section.questionType().equals(updateSectionQuestions.questionType())) {

        sectionsResponseList.add(buildSectionResponse(section, updateSectionQuestions));
        isUpdated = true;
      } else {
        sectionsResponseList.add(section);
      }
    }
    if (!isUpdated) {
      sectionsResponseList.add(buildNewSectionResponse(updateSectionQuestions));
    }

    return sectionsResponseList;
  }

  private QPGenProV2Dto.SectionsResponse buildSectionResponse(
      QPGenProV2Dto.SectionsResponse section,
      QpGenProDto.UpdateSectionQuestions updateSectionQuestions) {

    return QPGenProV2Dto.SectionsResponse.builder()
        .sectionId(section.sectionId())
        .sectionName(section.sectionName())
        .chapterName(section.chapterName())
        .chapterSlug(section.chapterSlug())
        .questionType(section.questionType())
        .selectedChapterQuestions(updateSectionQuestions.selectedChapterQuestions())
        .questionTagSlug(section.questionTagSlug())
        .questionComplexityName(section.questionComplexityName())
        .questionComplexitySlug(section.questionComplexitySlug())
        .totalAvailableQuestions(updateSectionQuestions.totalAvailableQuestions())
        .build();
  }

  private QPGenProV2Dto.SectionsResponse buildNewSectionResponse(
      QpGenProDto.UpdateSectionQuestions updateSectionQuestions) {

    return QPGenProV2Dto.SectionsResponse.builder()
        .chapterName(updateSectionQuestions.chapterName())
        .chapterSlug(updateSectionQuestions.chapterSlug())
        .questionType(updateSectionQuestions.questionType())
        .selectedChapterQuestions(updateSectionQuestions.selectedChapterQuestions())
        .questionComplexityName(updateSectionQuestions.questionComplexity())
        .questionComplexitySlug(updateSectionQuestions.questionComplexity())
        .totalAvailableQuestions(updateSectionQuestions.totalAvailableQuestions())
        .questionTagSlug(updateSectionQuestions.questionTags())
        .build();
  }

  private void addNewQuestions(
      QpGenProDto.QuestionsInfo questionsInfo,
      TestDefinitionSection testDefinitionSection,
      QpGenProDto.UpdateSectionQuestions updateSectionQuestions,
      String orgSlug,
      List<TestQuestion> filteredTestQuestionsList) {

    QpGenDto.QpGenResponse questionsNeeded;

    BluePrintDto.Section section;
    section =
        buildSection(
            testDefinitionSection,
            updateSectionQuestions,
            questionsInfo,
            filteredTestQuestionsList);
    questionsNeeded = getContentQuestions(section, orgSlug, updateSectionQuestions.chapterSlug());
    if (filteredTestQuestionsList.isEmpty()) {
      testQuestionRepository.saveAll(buildTestQuestions(questionsNeeded, testDefinitionSection, 0));
    } else {
      List<String> existingQuestionUuids =
          filteredTestQuestionsList.stream().map(TestQuestion::getQuestionUuid).toList();

      List<QpGenDto.QuestionsResponse> newQuestions;
      newQuestions =
          questionsNeeded.questionsList().stream()
              .filter(q -> !existingQuestionUuids.contains(q.uuid()))
              .limit(questionsInfo.selectedQuestions() - filteredTestQuestionsList.size())
              .toList();

      QpGenDto.QpGenResponse response;
      response =
          QpGenDto.QpGenResponse.builder()
              .sectionId(questionsNeeded.sectionId())
              .sectionName(questionsNeeded.sectionName())
              .questionsList(newQuestions)
              .build();

      testQuestionRepository.saveAll(buildTestQuestions(response, testDefinitionSection, 0));
    }
  }

  private BluePrintDto.Section buildSection(
      TestDefinitionSection testDefinitionSection,
      QpGenProDto.UpdateSectionQuestions updateSectionQuestions,
      QpGenProDto.QuestionsInfo questionsInfo,
      List<TestQuestion> filteredTestQuestionsList) {
    return BluePrintDto.Section.builder()
        .sectionId(testDefinitionSection.getId())
        .questionCount(
            filteredTestQuestionsList.isEmpty()
                ? questionsInfo.selectedQuestions()
                : questionsInfo.totalQuestions())
        .complexity(updateSectionQuestions.questionComplexity())
        .questionType(QuestionType.getByType(updateSectionQuestions.questionType()))
        .marks(Double.valueOf(questionsInfo.marks()))
        .sectionName(testDefinitionSection.getName())
        .build();
  }

  private QpGenDto.QpGenResponse getContentQuestions(
      BluePrintDto.Section section, String orgSlug, String chapterSlug) {
    var questions =
        contentService.getQuestionsForQpGenPro(
            Collections.singletonList(section), orgSlug, Collections.singletonList(chapterSlug));

    if (questions.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.QPGen.QuestionCount",
          new String[] {section.sectionName()});
    }
    return questions.get(0);
  }
}
