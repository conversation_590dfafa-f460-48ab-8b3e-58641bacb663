package com.wexl.retail.dac.knowledgemeter.model;

import static jakarta.persistence.GenerationType.IDENTITY;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "test_schedule_knowledge")
public class TestScheduleKnowledge extends Model {
  @Id
  @GeneratedValue(strategy = IDENTITY)
  private Long id;

  private String orgSlug;

  private String board;

  private String gradeSlug;

  private String gradeName;

  private String subjectSlug;

  private String chapterSlug;

  private String subTopicSlug;

  private Long studentId;

  @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  private List<TestScheduleKnowledgeDetail> knowledgeDetails = new ArrayList<>();
}
