package com.wexl.retail.notifications.dto;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum NotificationType {
  CLASSROOM,
  SECTION,
  INDIVIDUAL,
  GRADE,
  OR<PERSON><PERSON><PERSON>ATION,
  CIRCULAR,
  LEAVE_REQUEST,
  LEAVE_APPROVED,
  LEAVE_DISAPPROVED,
  APPOINTMENT_REQUEST,
  APPOINTMENT_APPROVED,
  APPOINTMENT_DISAPPROVED,
  STAFF_APPOINTMENT,
  FORUM,
  GATEPASS_REQUEST,
  GATEPASS_APPROVED,
  GATEPASS_DISAPPROVED,
  INFIRMARY
}
