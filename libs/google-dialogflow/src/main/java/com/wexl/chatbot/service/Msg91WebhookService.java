package com.wexl.chatbot.service;

import com.wexl.chatbot.DialogflowService;
import com.wexl.chatbot.Msg91Sender;
import com.wexl.chatbot.dto.WhatsAppBotDto;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.QuestionType;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.domain.TestDefinitionSection;
import com.wexl.retail.test.school.domain.TestQuestion;
import com.wexl.retail.test.school.domain.TestType;
import com.wexl.retail.test.school.dto.QuestionDto;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

@Service
@Slf4j
@RequiredArgsConstructor
public class Msg91WebhookService {
  private final DialogflowService df;
  private final Msg91Sender sender;
  private final UserRepository userRepository;
  private final TestDefinitionRepository testDefinitionRepository;
  private final ContentService contentService;
  private final TemplateEngine templateEngine;
  private final StorageService storageService;

  public void handleWebhook(WhatsAppBotDto.Msg91WebhookEvent evt) {
    // must reply < 5 s or MSG91 drops it
    log.info("Received MSG91 webhook event from sender: {}", evt.sender());

    String messageText;
    String senderNumber = evt.sender();

    // Extract message text - either from direct text field or from messages list
    if (evt.text() != null && !evt.text().isEmpty()) {
      messageText = evt.text();
      log.debug("Extracted message from text field: {}", messageText);
    } else if (evt.messages() != null && !evt.messages().isEmpty()) {
      WhatsAppBotDto.Messages message = evt.messages().get(0);
      if (message.text() != null && message.text().body() != null) {
        messageText = message.text().body();
        log.debug("Extracted message from messages list: {}", messageText);
      } else {
        // Handle non-text messages (like documents, images, etc.)
        messageText = "I received your message but can only process text for now.";
        log.info("Received non-text message type: {}", message.type());
      }
    } else {
      messageText = "I couldn't understand your message.";
      log.warn("Could not extract message text from webhook event");
    }

    try {
      // Process message with DialogFlow
      log.info("Processing message with DialogFlow: {}", messageText);
      // Using sender's number as session ID for conversation context
      String response = df.detectIntent(senderNumber, messageText);
      log.info("Received response from DialogFlow: {}", response);

      // Send response back to the user
      log.info("Sending response to sender: {}", senderNumber);
      sender.sendWhatsAppBotMessage(response, senderNumber);

    } catch (Exception e) {
      log.error("Error processing webhook event", e);
    }
  }

  public WhatsAppBotDto.CreateTestResponse createTest(WhatsAppBotDto.CreateTestRequest request) {
    var teacherData = userRepository.getUserByAuthUserId("ac9ad65d-72f6-4db3-a280-8b5dc3e236c6");
    var subjectSlug = changeSubjectToSlug(request.Grade(), request.Subject());
    var questionData = getQuestions(request, subjectSlug);
    var testDefinition = new TestDefinition();
    testDefinition.setTestName(request.TestName());
    testDefinition.setBoardSlug("cbse");
    testDefinition.setGradeSlug(request.Grade());
    testDefinition.setType(TestType.MOCK_TEST);
    testDefinition.setActive(true);
    testDefinition.setMessage("whatsApp-bot-test");
    testDefinition.setSubjectSlug(subjectSlug);
    testDefinition.setOrganization("cha242454");
    testDefinition.setTeacher(teacherData);
    testDefinition.setNoOfQuestions(request.QuestionCount());
    testDefinition.setTestDefinitionSections(
        buildTestDefinitionSections(testDefinition, questionData));
    testDefinitionRepository.save(testDefinition);
    String pdfUrl = saveQuestionsPdf(questionData);
    return WhatsAppBotDto.CreateTestResponse.builder()
        .id(testDefinition.getId())
        .pdfUrl(pdfUrl)
        .status(true)
        .message("Test created successfully")
        .build();
  }

  private String saveQuestionsPdf(QuestionDto.SearchQuestionResponse questionData) {
    Context context = new Context();
    context.setVariable("questions", questionData.questions());
    var html = templateEngine.process("generate-question-paper", context);
    String randomUuid = UUID.randomUUID().toString();
    final String path = "question-paper-" + randomUuid + ".html";
    storageService.uploadFile(
        html.getBytes(StandardCharsets.UTF_8), path, MediaType.TEXT_HTML_VALUE);
    return storageService.generatePreSignedUrlForFetch(path);
  }

  private List<TestDefinitionSection> buildTestDefinitionSections(
      TestDefinition testDefinition, QuestionDto.SearchQuestionResponse questionResponse) {
    var section = new TestDefinitionSection();
    section.setName("Section A");
    section.setSequenceNumber(1L);
    section.setNoOfQuestions(Long.valueOf(testDefinition.getNoOfQuestions()));
    section.setTestDefinition(testDefinition);
    section.setTestQuestions(buildTestQuestions(section, questionResponse));
    return List.of(section);
  }

  private List<TestQuestion> buildTestQuestions(
      TestDefinitionSection section, QuestionDto.SearchQuestionResponse questionResponse) {
    List<QuestionDto.Question> questionsList = questionResponse.questions();
    if (questionsList.isEmpty()) {
      log.warn("No questions found for the Chapter.");
      return List.of();
    }
    List<TestQuestion> testQuestions = new ArrayList<>();
    for (QuestionDto.Question question : questionsList) {
      TestQuestion testQuestion = new TestQuestion();
      testQuestion.setQuestionUuid(question.uuid());
      testQuestion.setChapterSlug(question.chapterSlug());
      testQuestion.setSubtopicSlug(question.subtopicSlug());
      testQuestion.setSubjectSlug(question.subjectSlug());
      testQuestion.setType(question.type().name());
      testQuestion.setMarks(question.marks());
      testQuestion.setNegativeMarks(question.negativeMarks());
      testQuestion.setTestDefinitionSection(section);
      if (question.type() == QuestionType.MCQ) {
        testQuestion.setMcqAnswer(question.mcq().answer());
      }
      testQuestions.add(testQuestion);
    }
    return testQuestions;
  }

  private QuestionDto.SearchQuestionResponse getQuestions(
      WhatsAppBotDto.CreateTestRequest request, String subjectSlug) {
    var questionsPdfRequest =
        QuestionDto.QuestionsPdfRequest.builder()
            .Subject(subjectSlug)
            .Grade(request.Grade())
            .Chapter(request.Chapter())
            .QuestionType(request.QuestionType())
            .QuestionCount(request.QuestionCount())
            .build();
    return contentService.getQuestionsFromContent(questionsPdfRequest);
  }

  private String changeSubjectToSlug(String grade, String subject) {
    grade = grade.toLowerCase();
    subject = subject.toLowerCase();

    Set<String> grades1to2 = Set.of("i", "ii");
    Set<String> grades3to4 = Set.of("iii", "iv");
    Set<String> grades1to5 = Set.of("i", "ii", "iii", "iv", "v");
    Set<String> grades6to10 = Set.of("vi", "vii", "viii", "ix", "x");
    Set<String> evsSubjects = Set.of("environmental studies", "evs");
    Set<String> socialSubjects = Set.of("social studies", "social");
    Set<String> mathsSubjects = Set.of("mathematics", "maths");

    if (grades1to2.contains(grade) && evsSubjects.contains(subject)) {
      return "evs-rama-sagar";
    } else if (grades3to4.contains(grade) && socialSubjects.contains(subject)) {
      return "social-rama-sagar";
    } else if (grades3to4.contains(grade) && subject.equalsIgnoreCase("science")) {
      return "science-rama-sagar";
    } else if (grades1to5.contains(grade) && subject.equalsIgnoreCase("english")) {
      return "english-rama-sagar";
    } else if (grade.equals("v") && mathsSubjects.contains(subject)) {
      return "mathematics-rachna-sagar";
    } else if (grade.equals("v") && subject.equalsIgnoreCase("science")) {
      return "science-rachna-sagar";
    } else if (grade.equals("v") && socialSubjects.contains(subject)) {
      return "social-rachna-sagar";
    } else if (grades6to10.contains(grade) && subject.equalsIgnoreCase("science")) {
      return "science-2024";
    } else if (grades6to10.contains(grade) && socialSubjects.contains(subject)) {
      return "social-2024";
    } else if (grades6to10.contains(grade) && subject.equalsIgnoreCase("english")) {
      return "english-2024";
    } else if (grades6to10.contains(grade) && mathsSubjects.contains(subject)) {
      return "mathematics-2024";
    }
    return null;
  }
}
