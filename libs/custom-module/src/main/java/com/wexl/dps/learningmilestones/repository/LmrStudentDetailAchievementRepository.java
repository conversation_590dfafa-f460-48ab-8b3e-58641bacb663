package com.wexl.dps.learningmilestones.repository;

import com.wexl.dps.learningmilestones.model.LmrStudentDetailAchievement;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface LmrStudentDetailAchievementRepository
    extends JpaRepository<LmrStudentDetailAchievement, Long> {
  Optional<LmrStudentDetailAchievement> findByStudentId(Long studentId);
}
