package com.wexl.admissiontests.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.model.Grade;
import com.wexl.retail.test.schedule.dto.TestStudentStatus;
import java.util.List;
import lombok.Builder;

public record AdmissionTestDto() {
  @Builder
  public record Request(
      String name,
      @JsonProperty("admission_no") Long admissionNo,
      @JsonProperty("phone_number") String phoneNumber,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("start_time") Long startTime,
      @JsonProperty("end_time") Long endTime,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("parent_name") String parentName,
      @JsonProperty("email_id") String emailId,
      @JsonProperty("school_name") String schoolName,
      String location,
      String referer) {}

  @Builder
  public record Response(
      long testDefinitionId,
      String testName,
      String subjectName,
      long startDate,
      long endDate,
      long scheduleTestId,
      String status,
      String category,
      @JsonProperty("schedule_test_state") String testState,
      @JsonProperty("schedule_test_uuid") String scheduleTestUuid,
      @JsonProperty("test_type") String testType,
      @JsonProperty("admission_test_id") Long admissionTestId) {}

  @Builder
  public record AdmissionTestsResponse(
      @JsonProperty("test_definition_id") long testDefinitionId,
      @JsonProperty("student_name") String studentName,
      @JsonProperty("student_auth_id") String studentAuthId,
      @JsonProperty("organization_name") String organizationName,
      @JsonProperty("created_at") long createdAt,
      @JsonProperty("schedule_test_id") long scheduleTestId,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("phone_no") String phoneNumber,
      @JsonProperty("id") long id,
      @JsonProperty("admission_no") Long admissionNo,
      @JsonProperty("test_details") BetTestDetails testDetails,
      @JsonProperty("status") TestStudentStatus status,
      @JsonProperty("location") String location,
      @JsonProperty("parent_name") String parentName,
      @JsonProperty("email_id") String emailId,
      @JsonProperty("school_name") String schoolName,
      @JsonProperty("test_center") String testCenter,
      String referer) {}

  @Builder
  public record BetTestDetails(
      @JsonProperty("section1_name") String section1Name,
      @JsonProperty("section1_marks") String section1marks,
      @JsonProperty("section2_name") String section2Name,
      @JsonProperty("section2_marks") String section2marks,
      @JsonProperty("section3_name") String section3Name,
      @JsonProperty("section3_marks") String section3marks,
      @JsonProperty("section4_name") String section4Name,
      @JsonProperty("section4_marks") String section4marks,
      @JsonProperty("over_all_score") Double overAllScore) {}

  @Builder
  public record AdmissionTestSummary(List<Grade> grades, List<String> testCenters) {}

  @Builder
  public record WeeklyMetric(String date, Long admissionCount) {}

  @Builder
  public record MetricResult(
      @JsonProperty("branch_name") String branchName,
      String referer,
      @JsonProperty("admission_count") Long count) {}
}
